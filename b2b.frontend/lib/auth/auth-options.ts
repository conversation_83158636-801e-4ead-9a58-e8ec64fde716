import { NextAuthOptions } from 'next-auth';
import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import { apiClient } from '@/lib/api/client';

// Extend the JWT type to include our custom fields
declare module 'next-auth/jwt' {
    interface JWT {
        id: string;
        name: string;
        email: string;
        roles: string[];
        accessToken: string;
        roleClaims: Record<string, Record<string, boolean>>;
    }
}

// Extend the Session type to include our custom fields
declare module "next-auth" {
    interface Session {
        user: {
            id: string;
            name: string;
            email: string;
            roles: string[];
        };
        accessToken: string;
        roleClaims: Record<string, Record<string, boolean>>;
    }

    interface User {
        id: string;
        name: string;
        email: string;
        roles: string[];
        token: string;
        rememberMe?: boolean;
    }
}

export const authOptions: NextAuthOptions = {
    providers: [
        CredentialsProvider({
            name: "Credentials",
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
            },
            async authorize(credentials: any) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }

                try {
                    const gatewayUrl = process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://localhost:33800';
                    const response = await apiClient.post(`${gatewayUrl}/admin/login`, {
                        email: credentials.email,
                        password: credentials.password,
                    });

                    if (response.status === 200 && response.data) {
                        // Return the user data from the API with remember me info
                        return {
                            id: response.data.id,
                            name: response.data.name,
                            email: response.data.email,
                            roles: response.data.roles,
                            token: response.data.token,
                            rememberMe: credentials.rememberMe === "true" || credentials.rememberMe === true,
                        } as any;
                    }

                    return null;
                } catch (error) {
                    console.error("Authentication error:", error);
                    return null;
                }
            },
        }),
    ],
    callbacks: {
        async jwt({ token, user, account }: any) {
            // Initial sign in
            if (user && account) {
                token.id = user.id;
                token.name = user.name;
                token.email = user.email;
                token.roles = user.roles;
                token.accessToken = user.token;

                // Set token expiry based on remember me (7 days vs 1 day)
                const rememberMe = user.rememberMe || false;
                const expiryTime = rememberMe ? 7 * 24 * 60 * 60 : 24 * 60 * 60; // 7 days or 1 day in seconds
                token.exp = Math.floor(Date.now() / 1000) + expiryTime;

                // Extract role claims from JWT token
                try {
                    // Parse the JWT token to get the claims
                    const tokenParts = user.token.split(".");
                    if (tokenParts.length === 3) {
                        const payload = JSON.parse(Buffer.from(tokenParts[1], "base64").toString());
                        if (payload.RoleClaims) {
                            token.roleClaims = JSON.parse(payload.RoleClaims);
                        }
                    }
                } catch (error) {
                    console.error("Error parsing JWT token:", error);
                }
            }

            // Check if token is expired
            if (token.exp && typeof token.exp === "number" && Date.now() / 1000 > token.exp) {
                console.log("Token expired, clearing session");
                return null; // This will trigger a logout
            }

            return token;
        },
        async session({ session, token }: any) {
            if (token) {
                session.user.id = token.id as string;
                session.user.name = token.name as string;
                session.user.email = token.email as string;
                session.user.roles = token.roles as string[];
                session.accessToken = token.accessToken as string;
                session.roleClaims = token.roleClaims || {};
            }
            return session;
        },
    },
    pages: {
        signIn: "/login",
        error: "/login",
    },
    session: {
        strategy: "jwt",
        maxAge: 7 * 24 * 60 * 60, // 7 days max (will be overridden by JWT callback)
    },
    secret: process.env.NEXTAUTH_SECRET,
};
