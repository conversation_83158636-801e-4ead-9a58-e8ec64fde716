import Credentials<PERSON>rovider from 'next-auth/providers/credentials';

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Customer login API call - use internal network communication
          const gatewayUrl = process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://api_gateway:33800';
          console.log('Calling login API:', `${gatewayUrl}/customer/login`);
          
          const response = await fetch(`${gatewayUrl}/customer/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          });

          const result = await response.json();
          console.log('Login API response:', result);

          if (response.ok && result.success && result.data?.isSuccessful) {
            const customerData = result.data;
            return {
              id: customerData.customer.id,
              email: customerData.customer.email,
              name: customerData.customer.nameSurname,
              accessToken: customerData.token,
              customer: customerData.customer,
              rememberMe: credentials.rememberMe === "true" || credentials.rememberMe === true,
            };
          }

          // Login failed
          console.log('Login failed:', result.message || result.data?.message);
          return null;
        } catch (error) {
          console.error('Customer login error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (user && account) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.accessToken = user.accessToken;
        token.customer = user.customer;

        // Set token expiry based on remember me (7 days vs 1 day)
        const rememberMe = user.rememberMe || false;
        const expiryTime = rememberMe ? 7 * 24 * 60 * 60 : 24 * 60 * 60; // 7 days or 1 day in seconds
        token.exp = Math.floor(Date.now() / 1000) + expiryTime;
      }

      // Validate token on each request
      if (token.accessToken) {
        try {
          const gatewayUrl = process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://api_gateway:33800';
          const response = await fetch(`${gatewayUrl}/customer/validate`, {
            headers: {
              'Authorization': `Bearer ${token.accessToken}`,
            },
          });

          if (!response.ok) {
            // Token is invalid, clear session
            console.log("Token validation failed, clearing session");
            return {};
          }

          const result = await response.json();
          if (result.success && result.data) {
            // Update customer data from validation response
            token.customer = result.data;
          }
        } catch (error) {
          console.error('Token validation error:', error);
          // Clear session on validation error
          return {};
        }
      }

      // Check if token is expired
      if (token.exp && typeof token.exp === "number" && Date.now() / 1000 > token.exp) {
        console.log("Token expired, clearing session");
        return {};
      }

      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id;
        session.user.email = token.email;
        session.user.name = token.name;
        session.accessToken = token.accessToken;
        session.customer = token.customer;
        session.test = "test"
      }
      return session;
    },
  },
  pages: {
    signIn: '/login',
    signUp: '/register'
  },
  session: {
    strategy: 'jwt',
    maxAge: 7 * 24 * 60 * 60, // 7 days max (will be overridden by JWT callback)
  },
  cookies: {
    sessionToken: {
      name: "next-auth.web-session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    callbackUrl: {
      name: "next-auth.web-callback-url",
      options: {
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    csrfToken: {
      name: "next-auth.web-csrf-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
};
